<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-04 19:53 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db___init___py.html">core\__init__.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t23">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t23"><data value='generate_response'>LLMAdapter.generate_response</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t28">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t28"><data value='stream_response'>LLMAdapter.stream_response</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t33">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t33"><data value='is_available'>LLMAdapter.is_available</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t41">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t41"><data value='init__'>OllamaAdapter.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t51">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t51"><data value='generate_response'>OllamaAdapter.generate_response</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t130">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t130"><data value='stream_response'>OllamaAdapter.stream_response</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t137">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t137"><data value='is_available'>OllamaAdapter.is_available</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t145">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t145"><data value='format_context'>OllamaAdapter._format_context</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t160">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t160"><data value='parse_tool_calls'>OllamaAdapter._parse_tool_calls</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t180">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t180"><data value='init__'>DeepseekAdapter.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t192">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t192"><data value='generate_response'>DeepseekAdapter.generate_response</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t289">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t289"><data value='stream_response'>DeepseekAdapter.stream_response</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t295">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t295"><data value='is_available'>DeepseekAdapter.is_available</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t308">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t308"><data value='format_context'>DeepseekAdapter._format_context</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t327">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t327"><data value='init__'>AICore.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t342">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t342"><data value='initialize_adapters'>AICore._initialize_adapters</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t357">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t357"><data value='get_available_providers'>AICore.get_available_providers</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t369">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t369"><data value='set_provider'>AICore.set_provider</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t384">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t384"><data value='generate_response'>AICore.generate_response</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t419">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t419"><data value='create_system_prompt'>AICore._create_system_prompt</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>6</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t16">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t16"><data value='init__'>ConfigManager.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t35">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t35"><data value='resolve_config_path'>ConfigManager._resolve_config_path</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t54">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t54"><data value='load_config'>ConfigManager.load_config</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t70">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t70"><data value='save_config'>ConfigManager.save_config</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t79">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t79"><data value='get'>ConfigManager.get</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t99">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t99"><data value='set'>ConfigManager.set</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t118">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t118"><data value='get_api_key'>ConfigManager.get_api_key</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t138">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t138"><data value='get_default_config'>ConfigManager._get_default_config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t206">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t206"><data value='get_storage_path'>ConfigManager.get_storage_path</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t19">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t19"><data value='init__'>ContextManager.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t35">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t35"><data value='gather_context'>ContextManager.gather_context</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t77">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t77"><data value='add_explicit_context'>ContextManager.add_explicit_context</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t99">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t99"><data value='remove_explicit_context'>ContextManager.remove_explicit_context</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t120">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t120"><data value='clear_explicit_context'>ContextManager.clear_explicit_context</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t125">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t125"><data value='get_directory_context'>ContextManager._get_directory_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t156">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t156"><data value='get_file_listings'>ContextManager._get_file_listings</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t222">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t222"><data value='get_git_context'>ContextManager._get_git_context</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t307">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t307"><data value='get_environment_context'>ContextManager._get_environment_context</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t327">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t327"><data value='get_explicit_context'>ContextManager._get_explicit_context</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t387">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t387"><data value='should_exclude'>ContextManager._should_exclude</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t20">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t20"><data value='init__'>Orchestrator.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t50">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t50"><data value='process_input'>Orchestrator.process_input</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t129">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t129"><data value='execute_tool_calls'>Orchestrator._execute_tool_calls</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t172">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t172"><data value='needs_confirmation'>Orchestrator._needs_confirmation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t190">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t190"><data value='format_tool_results_for_context'>Orchestrator._format_tool_results_for_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t210">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t210"><data value='create_session'>Orchestrator.create_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t214">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t214"><data value='list_sessions'>Orchestrator.list_sessions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t218">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t218"><data value='switch_session'>Orchestrator.switch_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t222">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t222"><data value='get_conversation_history'>Orchestrator.get_conversation_history</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t227">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t227"><data value='get_context_items'>Orchestrator.get_context_items</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t231">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t231"><data value='add_context_item'>Orchestrator.add_context_item</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t235">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t235"><data value='clear_explicit_context'>Orchestrator.clear_explicit_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t240">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t240"><data value='get_available_providers'>Orchestrator.get_available_providers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t244">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t244"><data value='get_current_provider'>Orchestrator.get_current_provider</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t248">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t248"><data value='set_provider'>Orchestrator.set_provider</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t253">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t253"><data value='get_status_info'>Orchestrator.get_status_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t274">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t274"><data value='shutdown'>Orchestrator.shutdown</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t18">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t18"><data value='init__'>SessionManager.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t37">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t37"><data value='load_sessions'>SessionManager._load_sessions</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t55">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t55"><data value='create_session'>SessionManager.create_session</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t77">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t77"><data value='get_session'>SessionManager.get_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t88">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t88"><data value='list_sessions'>SessionManager.list_sessions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t100">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t100"><data value='switch_session'>SessionManager.switch_session</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t116">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t116"><data value='delete_session'>SessionManager.delete_session</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t152">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t152"><data value='add_message'>SessionManager.add_message</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t179">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t179"><data value='get_conversation_history'>SessionManager.get_conversation_history</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t197">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t197"><data value='update_context'>SessionManager.update_context</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t210">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t210"><data value='get_context'>SessionManager.get_context</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t220">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t220"><data value='save_session'>SessionManager._save_session</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t233">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t233"><data value='convert_datetime'>SessionManager._save_session.convert_datetime</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t250">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t250"><data value='cleanup_old_sessions'>SessionManager.cleanup_old_sessions</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t25">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t25"><data value='init__'>ToolingEngine.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t61">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t61"><data value='execute_tool'>ToolingEngine.execute_tool</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t103">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t103"><data value='get_tool_schemas'>ToolingEngine.get_tool_schemas</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t273">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t273"><data value='execute_shell_command'>ToolingEngine.execute_shell_command</data></a></td>
                <td>13</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 13">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t318">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t318"><data value='read_file'>ToolingEngine.read_file</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t337">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t337"><data value='write_file'>ToolingEngine.write_file</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t364">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t364"><data value='list_directory'>ToolingEngine.list_directory</data></a></td>
                <td>16</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="11 16">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t406">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t406"><data value='create_directory'>ToolingEngine.create_directory</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t425">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t425"><data value='delete_file'>ToolingEngine.delete_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t449">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t449"><data value='delete_directory'>ToolingEngine.delete_directory</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t477">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t477"><data value='move_file'>ToolingEngine.move_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t504">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t504"><data value='copy_file'>ToolingEngine.copy_file</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t534">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t534"><data value='file_exists'>ToolingEngine.file_exists</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t553">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t553"><data value='web_search'>ToolingEngine.web_search</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t597">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t597"><data value='fetch_webpage'>ToolingEngine.fetch_webpage</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t636">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t636"><data value='git_status'>ToolingEngine.git_status</data></a></td>
                <td>34</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="25 34">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t705">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t705"><data value='git_add'>ToolingEngine.git_add</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t736">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t736"><data value='git_commit'>ToolingEngine.git_commit</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t767">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t767"><data value='git_push'>ToolingEngine.git_push</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t796">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t796"><data value='git_pull'>ToolingEngine.git_pull</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t825">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t825"><data value='git_branch'>ToolingEngine.git_branch</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t869">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t869"><data value='find_files'>ToolingEngine.find_files</data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t913">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t913"><data value='search_in_files'>ToolingEngine.search_in_files</data></a></td>
                <td>25</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="19 25">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t978">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t978"><data value='replace_in_files'>ToolingEngine.replace_in_files</data></a></td>
                <td>28</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="18 28">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049___init___py.html">models\__init__.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>93</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35___init___py.html">ui\__init__.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t19">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t19"><data value='init__'>BallAnimation.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t47">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t47"><data value='start'>BallAnimation.start</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t61">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t61"><data value='stop'>BallAnimation.stop</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t76">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t76"><data value='update_message'>BallAnimation.update_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t84">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t84"><data value='animate'>BallAnimation._animate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t107">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t107"><data value='create_display'>BallAnimation._create_display</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t137">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t137"><data value='format_elapsed_time'>BallAnimation._format_elapsed_time</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t155">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t155"><data value='init__'>SpinnerAnimation.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t174">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t174"><data value='start'>SpinnerAnimation.start</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t188">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t188"><data value='stop'>SpinnerAnimation.stop</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t203">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t203"><data value='update_message'>SpinnerAnimation.update_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t207">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t207"><data value='animate'>SpinnerAnimation._animate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t226">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t226"><data value='create_display'>SpinnerAnimation._create_display</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t241">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t241"><data value='init__'>ProgressAnimation.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t257">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t257"><data value='start'>ProgressAnimation.start</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t273">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t273"><data value='update'>ProgressAnimation.update</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t287">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t287"><data value='stop'>ProgressAnimation.stop</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t298">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t298"><data value='create_display'>ProgressAnimation._create_display</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t343">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t343"><data value='format_elapsed_time'>ProgressAnimation._format_elapsed_time</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t354">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t354"><data value='init__'>AnimationManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t366">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t366"><data value='start_ball_animation'>AnimationManager.start_ball_animation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t385">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t385"><data value='start_spinner'>AnimationManager.start_spinner</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t404">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t404"><data value='start_progress'>AnimationManager.start_progress</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t424">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t424"><data value='stop_current_animation'>AnimationManager.stop_current_animation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t33">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t33"><data value='init__'>CLIInterface.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t66">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t66"><data value='start'>CLIInterface.start</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t103">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t103"><data value='show_welcome'>CLIInterface._show_welcome</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t120">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t120"><data value='show_goodbye'>CLIInterface._show_goodbye</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t124">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t124"><data value='show_status_bar'>CLIInterface._show_status_bar</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t155">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t155"><data value='get_user_input'>CLIInterface._get_user_input</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t172">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t172"><data value='handle_internal_command'>CLIInterface._handle_internal_command</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t219">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t219"><data value='show_help'>CLIInterface._show_help</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t248">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t248"><data value='show_detailed_status'>CLIInterface._show_detailed_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t267">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t267"><data value='handle_session_command'>CLIInterface._handle_session_command</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t319">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t319"><data value='handle_context_command'>CLIInterface._handle_context_command</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t362">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t362"><data value='handle_diff_command'>CLIInterface._handle_diff_command</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t380">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t380"><data value='show_history'>CLIInterface._show_history</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t395">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t395"><data value='handle_provider_command'>CLIInterface._handle_provider_command</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t434">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t434"><data value='process_user_input'>CLIInterface._process_user_input</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t464">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t464"><data value='display_response'>CLIInterface._display_response</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t497">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t497"><data value='display_tool_result'>CLIInterface._display_tool_result</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t17">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t17"><data value='init__'>DiffViewer.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t29">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t29"><data value='show_diff'>DiffViewer.show_diff</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t55">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t55"><data value='show_side_by_side_diff'>DiffViewer.show_side_by_side_diff</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t81">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t81"><data value='show_inline_diff'>DiffViewer.show_inline_diff</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t126">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t126"><data value='display_unified_diff'>DiffViewer._display_unified_diff</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t166">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t166"><data value='create_text_panel'>DiffViewer._create_text_panel</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t208">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t208"><data value='compare_files'>DiffViewer.compare_files</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t244">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t244"><data value='show_git_diff'>DiffViewer.show_git_diff</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t283">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t283"><data value='display_git_diff'>DiffViewer._display_git_diff</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb___init___py.html">utils\__init__.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t16">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t16"><data value='detect_platform'>detect_platform</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t34">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t34"><data value='is_wsl'>is_wsl</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t47">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t47"><data value='find_executable'>find_executable</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t59">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t59"><data value='check_git_repository'>check_git_repository</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t83">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t83"><data value='get_file_type'>get_file_type</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t122">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t122"><data value='format_file_size'>format_file_size</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t144">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t144"><data value='truncate_text'>truncate_text</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t161">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t161"><data value='safe_filename'>safe_filename</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t187">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t187"><data value='parse_command_line'>parse_command_line</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t204">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t204"><data value='get_terminal_size'>get_terminal_size</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t217">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t217"><data value='is_binary_file'>is_binary_file</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t244">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t244"><data value='create_backup'>create_backup</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t270">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t270"><data value='validate_path'>validate_path</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t292">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t292"><data value='get_environment_info'>get_environment_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t326">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t326"><data value='setup_signal_handlers'>setup_signal_handlers</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t330">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html#t330"><data value='signal_handler'>setup_signal_handlers.signal_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1804</td>
                <td>1486</td>
                <td>12</td>
                <td class="right" data-ratio="318 1804">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-04 19:53 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
