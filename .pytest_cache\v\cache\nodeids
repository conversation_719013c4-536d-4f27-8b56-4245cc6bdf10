["tests/test_config_manager.py::TestConfigManager::test_api_key_retrieval", "tests/test_config_manager.py::TestConfigManager::test_get_configuration_value", "tests/test_config_manager.py::TestConfigManager::test_init_with_default_config", "tests/test_config_manager.py::TestConfigManager::test_save_and_load_config", "tests/test_config_manager.py::TestConfigManager::test_set_configuration_value", "tests/test_config_manager.py::TestConfigManager::test_storage_path_resolution", "tests/test_enhanced_tools.py::TestEnhancedTools::test_find_files_tool", "tests/test_enhanced_tools.py::TestEnhancedTools::test_git_add_tool", "tests/test_enhanced_tools.py::TestEnhancedTools::test_git_status_tool_no_repo", "tests/test_enhanced_tools.py::TestEnhancedTools::test_git_status_tool_with_repo", "tests/test_enhanced_tools.py::TestEnhancedTools::test_replace_in_files_dry_run", "tests/test_enhanced_tools.py::TestEnhancedTools::test_search_in_files_tool", "tests/test_enhanced_tools.py::TestEnhancedTools::test_tool_schemas_include_new_tools", "tests/test_enhanced_tools.py::TestEnhancedTools::test_unknown_enhanced_tool", "tests/test_tooling_engine.py::TestToolingEngine::test_create_directory_tool", "tests/test_tooling_engine.py::TestToolingEngine::test_file_exists_tool", "tests/test_tooling_engine.py::TestToolingEngine::test_list_directory_tool", "tests/test_tooling_engine.py::TestToolingEngine::test_read_write_file_tools", "tests/test_tooling_engine.py::TestToolingEngine::test_shell_command_tool", "tests/test_tooling_engine.py::TestToolingEngine::test_tool_error_handling", "tests/test_tooling_engine.py::TestToolingEngine::test_tool_schemas", "tests/test_tooling_engine.py::TestToolingEngine::test_unknown_tool"]