{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "9f5ed9245eb9030c7aedfad9b0cbeab4", "files": {"z_57760688d1f824db___init___py": {"hash": "b1fd44a3b63064f9c1b81aab070f7a78", "index": {"url": "z_57760688d1f824db___init___py.html", "file": "core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_ai_core_py": {"hash": "b7545c02c140492dba80ee34995d4839", "index": {"url": "z_57760688d1f824db_ai_core_py.html", "file": "core\\ai_core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 12, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_config_manager_py": {"hash": "07b06dedc0f8147627d292ee89d74278", "index": {"url": "z_57760688d1f824db_config_manager_py.html", "file": "core\\config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_context_manager_py": {"hash": "13afe16af125c068dfe2b74c8abadc41", "index": {"url": "z_57760688d1f824db_context_manager_py.html", "file": "core\\context_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_orchestrator_py": {"hash": "42119f3a9cc26b62925009907d2b77be", "index": {"url": "z_57760688d1f824db_orchestrator_py.html", "file": "core\\orchestrator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_session_manager_py": {"hash": "2422da6332ea715d06d628fef92248db", "index": {"url": "z_57760688d1f824db_session_manager_py.html", "file": "core\\session_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_tooling_engine_py": {"hash": "d3d53a2dc55f7624bfb1655e25dcd85d", "index": {"url": "z_57760688d1f824db_tooling_engine_py.html", "file": "core\\tooling_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 299, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e634d7a1dd90e049___init___py": {"hash": "8d1324b64d329c92103d956f396ae7aa", "index": {"url": "z_e634d7a1dd90e049___init___py.html", "file": "models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e634d7a1dd90e049_schemas_py": {"hash": "008e02b5d0bce963a6f4a1b34fe44643", "index": {"url": "z_e634d7a1dd90e049_schemas_py.html", "file": "models\\schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_19b8bf288843bf35___init___py": {"hash": "c4a14d32ea172921d2223ebdd51bdaed", "index": {"url": "z_19b8bf288843bf35___init___py.html", "file": "ui\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_19b8bf288843bf35_animations_py": {"hash": "00ca75a56a1f817e45a7fd57fe04fbf2", "index": {"url": "z_19b8bf288843bf35_animations_py.html", "file": "ui\\animations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 213, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_19b8bf288843bf35_cli_interface_py": {"hash": "1cf14a1da922afdded1d3f240a99a3c4", "index": {"url": "z_19b8bf288843bf35_cli_interface_py.html", "file": "ui\\cli_interface.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 310, "n_excluded": 0, "n_missing": 310, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_19b8bf288843bf35_diff_viewer_py": {"hash": "e2ce00e5641ea3aa31ae6c67e388d129", "index": {"url": "z_19b8bf288843bf35_diff_viewer_py.html", "file": "ui\\diff_viewer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c810615cce0f7acb___init___py": {"hash": "c8f3256ea9b19890a938dc5f4dd3cd5e", "index": {"url": "z_c810615cce0f7acb___init___py.html", "file": "utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c810615cce0f7acb_helpers_py": {"hash": "c2aec2b442a6288a4b1b9d8ec59fc61b", "index": {"url": "z_c810615cce0f7acb_helpers_py.html", "file": "utils\\helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 115, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}