<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-04 19:53 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db___init___py.html">core\__init__.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t19">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t19"><data value='LLMAdapter'>LLMAdapter</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t38">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t38"><data value='OllamaAdapter'>OllamaAdapter</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t177">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t177"><data value='DeepseekAdapter'>DeepseekAdapter</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t324">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html#t324"><data value='AICore'>AICore</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html">core\ai_core.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>6</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t13">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t13"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>55</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="45 55">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t16">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html#t16"><data value='ContextManager'>ContextManager</data></a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html">core\context_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t17">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html#t17"><data value='Orchestrator'>Orchestrator</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html">core\orchestrator.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t15">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html#t15"><data value='SessionManager'>SessionManager</data></a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html">core\session_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t22">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html#t22"><data value='ToolingEngine'>ToolingEngine</data></a></td>
                <td>262</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="126 262">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html">core\tooling_engine.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049___init___py.html">models\__init__.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t9">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t9"><data value='LLMProvider'>LLMProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t15">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t15"><data value='ToolType'>ToolType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t23">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t23"><data value='MessageRole'>MessageRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t31">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t31"><data value='ToolCall'>ToolCall</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t38">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t38"><data value='ToolResult'>ToolResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t48">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t48"><data value='Message'>Message</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t58">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t58"><data value='Session'>Session</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t69">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t69"><data value='ContextItem'>ContextItem</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t78">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t78"><data value='LLMRequest'>LLMRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t89">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t89"><data value='LLMResponse'>LLMResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t98">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t98"><data value='FileOperation'>FileOperation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t108">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t108"><data value='ShellCommand'>ShellCommand</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t118">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t118"><data value='WebSearchQuery'>WebSearchQuery</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t126">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t126"><data value='DiffRequest'>DiffRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t135">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html#t135"><data value='AnimationState'>AnimationState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html">models\schemas.py</a></td>
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35___init___py.html">ui\__init__.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t16">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t16"><data value='BallAnimation'>BallAnimation</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t152">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t152"><data value='SpinnerAnimation'>SpinnerAnimation</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t238">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t238"><data value='ProgressAnimation'>ProgressAnimation</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t351">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html#t351"><data value='AnimationManager'>AnimationManager</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html">ui\animations.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t30">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html#t30"><data value='CLIInterface'>CLIInterface</data></a></td>
                <td>270</td>
                <td>270</td>
                <td>0</td>
                <td class="right" data-ratio="0 270">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html">ui\cli_interface.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t14">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html#t14"><data value='DiffViewer'>DiffViewer</data></a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html">ui\diff_viewer.py</a></td>
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb___init___py.html">utils\__init__.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html">utils\helpers.py</a></td>
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1804</td>
                <td>1486</td>
                <td>12</td>
                <td class="right" data-ratio="318 1804">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-04 19:53 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
