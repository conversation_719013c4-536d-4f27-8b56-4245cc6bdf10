<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-04 19:53 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db___init___py.html">core\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_ai_core_py.html">core\ai_core.py</a></td>
                <td>177</td>
                <td>177</td>
                <td>12</td>
                <td class="right" data-ratio="0 177">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html">core\config_manager.py</a></td>
                <td>72</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="62 72">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_context_manager_py.html">core\context_manager.py</a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_orchestrator_py.html">core\orchestrator.py</a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_session_manager_py.html">core\session_manager.py</a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_tooling_engine_py.html">core\tooling_engine.py</a></td>
                <td>299</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="163 299">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049___init___py.html">models\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e634d7a1dd90e049_schemas_py.html">models\schemas.py</a></td>
                <td>93</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35___init___py.html">ui\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_animations_py.html">ui\animations.py</a></td>
                <td>213</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="0 213">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_cli_interface_py.html">ui\cli_interface.py</a></td>
                <td>310</td>
                <td>310</td>
                <td>0</td>
                <td class="right" data-ratio="0 310">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_19b8bf288843bf35_diff_viewer_py.html">ui\diff_viewer.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb___init___py.html">utils\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c810615cce0f7acb_helpers_py.html">utils\helpers.py</a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1804</td>
                <td>1486</td>
                <td>12</td>
                <td class="right" data-ratio="318 1804">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-04 19:53 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_c810615cce0f7acb_helpers_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_57760688d1f824db___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
